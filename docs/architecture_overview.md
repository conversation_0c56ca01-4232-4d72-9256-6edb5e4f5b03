# Architecture Overview

This document outlines the high-level architecture of the Diogenes AI Chatbot application, a comprehensive social platform with advanced AI capabilities.

## Application Overview

Diogenes AI Chatbot is a multi-platform Flutter application that combines social networking features with cutting-edge AI capabilities. The app supports real-time chat, AI interactions, content creation, offline AI models, and various specialized features like tutoring, writing assistance, and image generation.

## Core Architecture Layers

### 1. User Interface Layer

-   **Framework**: Flutter with multi-platform support (Android, iOS, Web, Desktop)
-   **Structure**: Feature-first architecture in `lib/features/` with clean separation of concerns
-   **Navigation**: Centralized routing using `go_router` and `GetMaterialApp`
-   **Theming**: Dual theme support (light/dark) with `AppTheme` class
-   **Localization**: Multi-language support with `AppLocalizations`

### 2. State Management Layer

-   **Primary**: Mixed approach using Provider, BLoC/Cubit, and Riverpod
-   **Business Logic**: Cubit/BLoC classes for complex state management
-   **Simple State**: Provider with ChangeNotifier for UI state
-   **Dependency Injection**: GetIt service locator pattern
-   **Future Direction**: Gradual migration to Riverpod for consistency

### 3. Backend Services Layer

-   **Primary Backend**: Firebase ecosystem
    -   **Authentication**: Firebase Auth with multiple providers (Google, Apple, Twitter)
    -   **Database**: Firestore for real-time data synchronization
    -   **Storage**: Firebase Storage for media files
    -   **Functions**: TypeScript Cloud Functions for server-side logic
    -   **Analytics**: Firebase Analytics and Crashlytics
    -   **Remote Config**: Feature flags and configuration management
-   **Additional Services**: Custom WebSocket connections for real-time AI chat

### 4. AI Integration Layer

-   **Online Models**:
    -   OpenAI (GPT models) via `dart_openai` and `chatgpt_completions`
    -   Google Gemini via `google_generative_ai` and `firebase_vertexai`
    -   Anthropic Claude via `anthropic_sdk_dart`
    -   Mistral AI via `langchain_mistralai`
-   **Offline Models**:
    -   Custom `flutter_gemma` package for on-device Gemma models
    -   `maid_llm` package for Llama.cpp integration
    -   Stable Diffusion for local image generation
-   **AI Frameworks**: LangChain integration for complex AI workflows

### 5. Data Persistence Layer

-   **Local Database**: SQLite via `drift` package for structured data
-   **Key-Value Storage**: Hive and ObjectBox for lightweight persistence
-   **Shared Preferences**: User settings and preferences
-   **Cache Management**: Memory and disk caching for performance

## Feature Architecture

### Core Features

1. **Real-time Chat**: Multi-user chat with friends and AI bots
2. **AI Chat**: Conversations with various AI models (online/offline)
3. **Social Features**: Posts, timeline, user profiles, following system
4. **Content Creation**: Writing assistance, story generation, document creation

### Specialized Features

1. **AI Tutor**: Educational content and personalized learning
2. **Code Editor**: AI-assisted code editing and generation
3. **Image Generation**: Both online and offline image creation
4. **PDF Processing**: PDF to Markdown conversion and analysis
5. **Podcast Features**: Audio processing and transcription
6. **Payment System**: In-app purchases and subscription management
7. **Real-time Communication**: WebRTC for voice/video calls

## Data Flow Architecture

```mermaid
graph TD
    A[UI Widgets] --> B[BLoC/Cubit/Provider]
    B --> C[Services Layer]
    C --> D[Repository Layer]
    D --> E[Data Sources]
    E --> F[External APIs]
    E --> G[Firebase]
    E --> H[Local Database]
    E --> I[AI Models]

    J[WebSocket] --> C
    K[Push Notifications] --> C
    L[Background Tasks] --> C
```

## Platform-Specific Implementations

### Mobile (Android/iOS)

-   Native AI model integration
-   Push notifications
-   Camera and media access
-   Background processing

### Web

-   Progressive Web App capabilities
-   Limited offline AI functionality
-   Web-specific UI adaptations

### Desktop (Windows/macOS/Linux)

-   Full offline AI capabilities
-   Desktop-specific UI patterns
-   System tray integration
-   File system access

## Security Architecture

-   **Authentication**: Firebase Auth with JWT tokens
-   **API Security**: Token-based authentication for all API calls
-   **Data Encryption**: Encrypted local storage for sensitive data
-   **Privacy**: User data protection and GDPR compliance

## Performance Considerations

-   **Lazy Loading**: Feature-based code splitting
-   **Caching**: Multi-level caching strategy
-   **Offline Support**: Comprehensive offline functionality
-   **Memory Management**: Efficient handling of large AI models
-   **Background Processing**: Non-blocking AI operations

## Current Technical Debt

-   Mixed state management patterns need consolidation
-   Some legacy code using outdated patterns
-   Inconsistent error handling across features
-   Need for better testing coverage

## Future Improvements

-   **State Management**: Complete migration to Riverpod
-   **Code Generation**: Increased use of `flutter_gen` and `freezed`
-   **API Abstraction**: Unified interface for all AI providers
-   **Testing**: Comprehensive test suite with CI/CD integration
-   **Performance**: Advanced caching and optimization strategies
-   **Modularization**: Extract features into reusable packages
