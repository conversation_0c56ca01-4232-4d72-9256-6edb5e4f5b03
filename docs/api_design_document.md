# API Design Document
# Diogenes AI Chatbot Platform

## Overview

This document outlines the API architecture for the Diogenes AI Chatbot platform, including internal service APIs, external integrations, and the planned public API for third-party developers.

## API Architecture

### 1. Internal APIs

#### 1.1 AI Service API
Unified interface for all AI model interactions, both online and offline.

```dart
abstract class AIService {
  Future<AIResponse> generateText(AIRequest request);
  Future<AIResponse> generateImage(ImageRequest request);
  Stream<String> streamResponse(AIRequest request);
  Future<List<AIModel>> getAvailableModels();
  Future<void> switchModel(String modelId);
}
```

**Endpoints:**
- `POST /api/ai/generate` - Generate AI responses
- `POST /api/ai/stream` - Stream AI responses
- `GET /api/ai/models` - List available models
- `PUT /api/ai/model/{id}` - Switch active model

#### 1.2 Chat Service API
Real-time messaging and conversation management.

```dart
abstract class ChatService {
  Future<Conversation> createConversation(CreateConversationRequest request);
  Future<Message> sendMessage(SendMessageRequest request);
  Stream<Message> getMessageStream(String conversationId);
  Future<List<Conversation>> getUserConversations(String userId);
}
```

**WebSocket Endpoints:**
- `ws://api/chat/connect` - Real-time chat connection
- `ws://api/ai-chat/connect` - AI chat with streaming responses

#### 1.3 User Management API
User authentication, profiles, and social features.

```dart
abstract class UserService {
  Future<User> createUser(CreateUserRequest request);
  Future<User> updateProfile(UpdateProfileRequest request);
  Future<List<User>> searchUsers(String query);
  Future<void> followUser(String userId);
  Future<List<User>> getFollowers(String userId);
}
```

### 2. External API Integrations

#### 2.1 AI Provider APIs

**OpenAI Integration**
```dart
class OpenAIProvider implements AIProvider {
  static const String baseUrl = 'https://api.openai.com/v1';
  
  @override
  Future<AIResponse> generateText(AIRequest request) async {
    // Implementation using dart_openai package
  }
}
```

**Google Gemini Integration**
```dart
class GeminiProvider implements AIProvider {
  @override
  Future<AIResponse> generateText(AIRequest request) async {
    // Implementation using google_generative_ai package
  }
}
```

**Anthropic Claude Integration**
```dart
class ClaudeProvider implements AIProvider {
  @override
  Future<AIResponse> generateText(AIRequest request) async {
    // Implementation using anthropic_sdk_dart package
  }
}
```

#### 2.2 Firebase Integration

**Authentication**
```dart
class FirebaseAuthService {
  Future<AuthResult> signInWithEmail(String email, String password);
  Future<AuthResult> signInWithGoogle();
  Future<AuthResult> signInWithApple();
  Future<void> signOut();
}
```

**Firestore Database**
```dart
class FirestoreService {
  Future<void> saveDocument(String collection, Map<String, dynamic> data);
  Future<Map<String, dynamic>?> getDocument(String collection, String id);
  Stream<List<Map<String, dynamic>>> streamCollection(String collection);
}
```

### 3. Public API (Planned)

#### 3.1 Authentication
All API requests require authentication via JWT tokens.

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

#### 3.2 Core Endpoints

**AI Generation**
```http
POST /api/v1/ai/generate
{
  "model": "gpt-4",
  "prompt": "Write a story about...",
  "max_tokens": 1000,
  "temperature": 0.7
}
```

**Chat Management**
```http
GET /api/v1/conversations
POST /api/v1/conversations
GET /api/v1/conversations/{id}/messages
POST /api/v1/conversations/{id}/messages
```

**User Management**
```http
GET /api/v1/users/profile
PUT /api/v1/users/profile
GET /api/v1/users/{id}
POST /api/v1/users/{id}/follow
```

#### 3.3 Webhook Support
```http
POST /api/v1/webhooks/register
{
  "url": "https://your-app.com/webhook",
  "events": ["message.created", "ai.response.completed"],
  "secret": "webhook_secret"
}
```

## Data Models

### 3.1 Core Models

**User Model**
```dart
class User {
  final String id;
  final String email;
  final String displayName;
  final String? avatarUrl;
  final DateTime createdAt;
  final UserPreferences preferences;
}
```

**Message Model**
```dart
class Message {
  final String id;
  final String conversationId;
  final String senderId;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final List<Attachment> attachments;
}
```

**AI Request Model**
```dart
class AIRequest {
  final String prompt;
  final String modelId;
  final Map<String, dynamic> parameters;
  final List<String> context;
  final String? userId;
}
```

### 3.2 Response Models

**Standard API Response**
```dart
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final Map<String, dynamic>? metadata;
}
```

**Paginated Response**
```dart
class PaginatedResponse<T> {
  final List<T> items;
  final int totalCount;
  final int page;
  final int pageSize;
  final bool hasNext;
}
```

## Error Handling

### Error Codes
- `400` - Bad Request (Invalid parameters)
- `401` - Unauthorized (Invalid or missing token)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Resource doesn't exist)
- `429` - Rate Limited (Too many requests)
- `500` - Internal Server Error
- `503` - Service Unavailable (AI model offline)

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "INVALID_MODEL",
    "message": "The specified AI model is not available",
    "details": {
      "model_id": "gpt-5",
      "available_models": ["gpt-4", "gpt-3.5-turbo"]
    }
  }
}
```

## Rate Limiting

### Limits by Plan
- **Free Tier**: 100 requests/hour, 1,000 requests/day
- **Premium**: 1,000 requests/hour, 10,000 requests/day
- **Enterprise**: Custom limits

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Security

### Authentication Flow
1. User authenticates via Firebase Auth
2. Client receives JWT token
3. Token included in API requests
4. Server validates token with Firebase

### API Key Management
```dart
class APIKeyService {
  Future<String> generateAPIKey(String userId);
  Future<void> revokeAPIKey(String keyId);
  Future<List<APIKey>> getUserAPIKeys(String userId);
}
```

### Data Privacy
- All sensitive data encrypted at rest
- PII data anonymized in logs
- GDPR compliance for EU users
- User data deletion on request

## Monitoring and Analytics

### Metrics Tracked
- API request volume and latency
- Error rates by endpoint
- AI model performance and usage
- User engagement metrics

### Logging
```dart
class APILogger {
  void logRequest(String endpoint, Map<String, dynamic> params);
  void logResponse(String endpoint, int statusCode, Duration duration);
  void logError(String endpoint, Exception error);
}
```

## Versioning Strategy

### URL Versioning
- Current: `/api/v1/`
- Future: `/api/v2/`

### Backward Compatibility
- Maintain v1 for 12 months after v2 release
- Deprecation warnings in response headers
- Migration guides for breaking changes

## SDK and Client Libraries

### Planned SDKs
- **Dart/Flutter**: Native integration
- **JavaScript/TypeScript**: Web and Node.js
- **Python**: Data science and automation
- **Swift**: iOS native apps
- **Kotlin**: Android native apps

### Example Usage
```dart
// Dart SDK
final client = DiogenesAPIClient(apiKey: 'your_api_key');
final response = await client.ai.generateText(
  prompt: 'Write a poem about AI',
  model: 'gpt-4',
);
```

## Testing Strategy

### API Testing
- Unit tests for all endpoints
- Integration tests with real AI models
- Load testing for performance
- Security testing for vulnerabilities

### Mock Services
```dart
class MockAIService implements AIService {
  @override
  Future<AIResponse> generateText(AIRequest request) async {
    return AIResponse(
      content: 'Mock AI response',
      model: request.modelId,
      usage: Usage(promptTokens: 10, completionTokens: 20),
    );
  }
}
```

## Documentation

### API Documentation
- OpenAPI/Swagger specification
- Interactive API explorer
- Code examples in multiple languages
- Postman collection

### Developer Resources
- Getting started guide
- Best practices documentation
- Rate limiting guidelines
- Error handling examples

## Future Enhancements

### Planned Features
- GraphQL API support
- Real-time subscriptions
- Batch processing endpoints
- Advanced analytics API
- Plugin marketplace API

### Performance Improvements
- Response caching
- CDN integration
- Database optimization
- Async processing queues

This API design provides a solid foundation for the Diogenes AI Chatbot platform while maintaining flexibility for future enhancements and third-party integrations.
