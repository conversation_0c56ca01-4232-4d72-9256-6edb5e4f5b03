# Technical Architecture 2024
# Diogenes AI Chatbot Platform

*Last Updated: December 2024*

This document outlines the current technical architecture of the Diogenes AI Chatbot platform, including technology stack, system design, and implementation patterns.

## Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        A[Flutter Mobile Apps] --> D[Unified Client Interface]
        B[Flutter Web App] --> D
        C[Flutter Desktop Apps] --> D
    end
    
    subgraph "Service Layer"
        D --> E[Authentication Service]
        D --> F[AI Service Coordinator]
        D --> G[Social Service Layer]
        D --> H[Payment Service]
    end
    
    subgraph "Data Layer"
        E --> I[Firebase Auth]
        F --> J[AI Provider APIs]
        F --> K[Local AI Models]
        G --> L[Cloud Firestore]
        H --> M[Stripe/Payment APIs]
    end
    
    subgraph "Infrastructure"
        I --> N[Firebase Platform]
        L --> N
        J --> O[External AI APIs]
        K --> P[Device Storage]
        M --> Q[Payment Processors]
    end
```

## Technology Stack

### Frontend Framework
- **Flutter 3.8.0** - Cross-platform UI framework
- **Dart SDK 3.8.0** - Programming language
- **Material Design 3** - UI design system
- **Responsive Framework** - Adaptive layouts

### State Management
- **Provider** - Primary state management
- **Riverpod** - Modern state management (migration in progress)
- **BLoC/Cubit** - Complex state scenarios
- **GetX** - Navigation and dependency injection

### Backend Services
- **Firebase Platform** - Backend-as-a-Service
  - Authentication
  - Cloud Firestore (NoSQL database)
  - Cloud Storage
  - Cloud Functions
  - Remote Config
  - Analytics
  - Crashlytics

### AI Integration
- **Online APIs**:
  - OpenAI API (GPT models, DALL-E)
  - Google Gemini API
  - Anthropic Claude API
  - Mistral AI API
  - Ollama REST API

- **Offline Models**:
  - Flutter Gemma (custom package)
  - Llama.cpp via llama_sdk
  - Stable Diffusion (custom package)
  - Whisper for STT
  - OuteTTS for TTS

### Development Tools
- **Build System**: Flutter build tools
- **CI/CD**: GitHub Actions
- **Testing**: Flutter Test, Integration Tests
- **Code Generation**: build_runner, json_serializable
- **Dependency Management**: pub.dev packages

## System Architecture Patterns

### 1. Clean Architecture Implementation

```mermaid
graph LR
    subgraph "Presentation Layer"
        A[Pages/Widgets] --> B[ViewModels/Controllers]
    end
    
    subgraph "Domain Layer"
        C[Use Cases] --> D[Entities]
        C --> E[Repository Interfaces]
    end
    
    subgraph "Data Layer"
        F[Repository Implementations] --> G[Data Sources]
        G --> H[Local Storage]
        G --> I[Remote APIs]
    end
    
    B --> C
    E --> F
```

### 2. Service Locator Pattern
- **GetIt** for dependency injection
- **Singleton Services** for core functionality
- **Lazy Loading** for performance optimization
- **Service Registration** at app startup

### 3. Repository Pattern
- **Abstract Repositories** for data access
- **Multiple Data Sources** (local, remote, cache)
- **Data Transformation** between layers
- **Error Handling** and retry logic

## Core Services Architecture

### Authentication Service
```dart
class AuthService {
  final FirebaseAuth _auth;
  final GoogleSignIn _googleSignIn;
  
  Stream<AuthStatus> get authStatusStream;
  Future<UserCredential> signInWithEmailAndPassword();
  Future<UserCredential> signInWithGoogle();
  Future<void> signOut();
}
```

### AI Service Coordinator
```dart
class AIServiceCoordinator {
  final Map<String, AIProvider> _providers;
  
  Future<AIResponse> generateResponse(AIRequest request);
  Stream<String> streamResponse(AIRequest request);
  Future<List<AIModel>> getAvailableModels();
  Future<void> switchProvider(String providerId);
}
```

### Social Service Layer
```dart
class SocialService {
  final PostRepository _postRepository;
  final ChatRepository _chatRepository;
  final UserRepository _userRepository;
  
  Future<List<Post>> getTimelinePosts();
  Future<void> createPost(Post post);
  Future<void> sendMessage(Message message);
}
```

## Data Architecture

### Database Design (Cloud Firestore)

```mermaid
erDiagram
    Users ||--o{ Posts : creates
    Users ||--o{ Messages : sends
    Users ||--o{ ChatRooms : participates
    Posts ||--o{ Comments : has
    Posts ||--o{ Reactions : receives
    ChatRooms ||--o{ Messages : contains
    
    Users {
        string uid PK
        string email
        string displayName
        string photoURL
        timestamp createdAt
        map preferences
    }
    
    Posts {
        string postId PK
        string authorId FK
        string content
        array mediaUrls
        timestamp createdAt
        number likesCount
        number commentsCount
    }
    
    ChatRooms {
        string chatRoomId PK
        array memberIds
        string groupAdminId
        timestamp lastActivity
        map metadata
    }
    
    Messages {
        string messageId PK
        string chatRoomId FK
        string senderId FK
        string content
        string type
        timestamp timestamp
    }
```

### Local Storage Strategy
- **Hive** - Lightweight key-value storage
- **SQLite** - Relational data with sqflite
- **Shared Preferences** - Simple key-value pairs
- **File System** - Large files and AI models

### Caching Strategy
- **Memory Cache** - Frequently accessed data
- **Disk Cache** - Images and media files
- **Network Cache** - API responses
- **Model Cache** - AI model files

## AI Model Management

### Model Storage Architecture
```mermaid
graph TB
    A[Model Registry] --> B[Download Manager]
    B --> C[Local Storage]
    C --> D[Model Loader]
    D --> E[Inference Engine]
    
    F[Remote Models] --> G[API Gateway]
    G --> H[Response Cache]
    H --> I[Client Interface]
    
    E --> J[Unified AI Interface]
    I --> J
```

### Model Lifecycle Management
1. **Discovery** - Available models from providers
2. **Download** - Progressive download with resume
3. **Validation** - Model integrity checks
4. **Loading** - Memory-mapped file loading
5. **Inference** - Optimized execution
6. **Cleanup** - Memory management and cleanup

## Security Architecture

### Authentication Security
- **Firebase Authentication** with industry standards
- **OAuth 2.0** for third-party providers
- **JWT Tokens** for session management
- **Multi-factor Authentication** support

### Data Security
- **End-to-end Encryption** for sensitive data
- **HTTPS/TLS** for all network communication
- **Local Encryption** for stored credentials
- **Privacy Controls** for user data

### API Security
- **API Key Management** with environment variables
- **Rate Limiting** to prevent abuse
- **Request Validation** and sanitization
- **Error Handling** without information leakage

## Performance Optimization

### Application Performance
- **Lazy Loading** for heavy components
- **Image Optimization** with caching
- **Memory Management** for AI models
- **Background Processing** for non-critical tasks

### Network Optimization
- **Request Batching** for efficiency
- **Response Compression** to reduce bandwidth
- **Offline Support** with local caching
- **Progressive Loading** for large datasets

### AI Model Optimization
- **Model Quantization** for smaller sizes
- **Parallel Processing** where possible
- **Memory Mapping** for efficient loading
- **GPU Acceleration** when available

## Deployment Architecture

### Mobile Deployment
- **Android**: Google Play Store with AAB format
- **iOS**: Apple App Store with IPA format
- **Code Signing**: Automated with CI/CD
- **App Distribution**: Firebase App Distribution for testing

### Web Deployment
- **Progressive Web App** with service workers
- **Static Hosting** on Firebase Hosting
- **CDN Distribution** for global performance
- **Responsive Design** for all devices

### Desktop Deployment
- **Windows**: MSIX packages for Microsoft Store
- **macOS**: DMG packages with notarization
- **Linux**: AppImage for universal compatibility

## Monitoring & Analytics

### Application Monitoring
- **Firebase Crashlytics** for crash reporting
- **Firebase Performance** for performance metrics
- **Firebase Analytics** for user behavior
- **Custom Logging** for debugging

### Business Analytics
- **User Engagement** metrics
- **Feature Usage** tracking
- **AI Model Performance** monitoring
- **Revenue Analytics** for monetization

## Development Workflow

### Code Organization
```
lib/
├── core/           # Core utilities and constants
├── features/       # Feature-based modules
├── models/         # Data models
├── pages/          # UI pages
├── services/       # Business logic services
├── widgets/        # Reusable UI components
└── utils/          # Helper utilities
```

### Testing Strategy
- **Unit Tests** for business logic
- **Widget Tests** for UI components
- **Integration Tests** for user flows
- **Golden Tests** for UI consistency

### CI/CD Pipeline
1. **Code Commit** triggers automated builds
2. **Automated Testing** runs all test suites
3. **Code Quality** checks with linting
4. **Build Artifacts** for multiple platforms
5. **Deployment** to staging/production

---

*This architecture document reflects the current implementation as of December 2024 and serves as a reference for development and maintenance.*
